import { DatePicker, Form, Input, InputNumber, Select } from 'antd';
import dayjs from 'dayjs';
import React, { useEffect, useState } from 'react';
import { Project, ProjectExpense, PurchaseOrder } from '../../types';
import { calculateDueDate } from '../../utils/helpers';

const { Option } = Select;

interface PurchaseOrderFormFieldsProps {
  projects: Project[];
  projectExpenses: ProjectExpense[];
  tagOptions: { label: string; value: string }[];
  categoryTagOptions: { label: string; value: string }[];
  handleProjectChange: (value: number) => void;
  isEditing?: boolean;
  selectedProject?: number | null;
  hideProjectSelect?: boolean;
  form?: any;
  initialValues?: PurchaseOrder | null;
}

const PurchaseOrderFormFields: React.FC<PurchaseOrderFormFieldsProps> = ({
  projects,
  projectExpenses,
  tagOptions,
  categoryTagOptions,
  handleProjectChange,
  isEditing = false,
  selectedProject,
  hideProjectSelect = false,
  form,
  initialValues,
}) => {
  const [calculatedDueDate, setCalculatedDueDate] = useState<dayjs.Dayjs | null>(null);

  // Use Form.useWatch to watch for field changes
  const issueDate = Form.useWatch('issue_date', form);
  const leadTime = Form.useWatch('lead_time', form);
  const terms = Form.useWatch('terms', form);

  // Set initial values when component mounts or initialValues change
  useEffect(() => {
    if (form && initialValues && isEditing) {
      form.setFieldsValue({
        project_id: initialValues.project_id,
        po_number: initialValues.po_number,
        description: initialValues.description,
        issue_date: initialValues.issue_date ? dayjs(initialValues.issue_date) : null,
        lead_time: initialValues.lead_time,
        amount: initialValues.amount,
        terms: initialValues.terms,
        category_tag_name: initialValues.category_tag ? initialValues.category_tag.name : '',
        due_date: initialValues.due_date ? dayjs(initialValues.due_date) : null,
        tag_names: initialValues.tags ? initialValues.tags.map(tag => tag.name) : [],
      });
    } else if (!isEditing) {
      // Reset calculated due date when not editing (new modal)
      setCalculatedDueDate(null);
    }
  }, [form, initialValues, isEditing]);

  // Reset calculated due date when form is reset
  useEffect(() => {
    if (!issueDate || !leadTime || !terms) {
      setCalculatedDueDate(null);
    }
  }, [issueDate, leadTime, terms]);

  // Calculate due date when issue date, lead time, or terms change
  useEffect(() => {
    if (issueDate && leadTime && terms) {
      const dueDate = calculateDueDate(issueDate, leadTime, terms);
      setCalculatedDueDate(dueDate);

      // Set the calculated due date as a form field value
      if (form) {
        form.setFieldsValue({ due_date: dueDate });
      }
    } else {
      setCalculatedDueDate(null);

      // Clear the due date field if not all required fields are filled
      if (form) {
        form.setFieldsValue({ due_date: undefined });
      }
    }
  }, [issueDate, leadTime, terms, form]);

  let categoryPlaceholder = 'Select a category';
  if (selectedProject && categoryTagOptions.length === 0) {
    categoryPlaceholder = 'No categories available';
  } else if (!selectedProject) {
    categoryPlaceholder = 'Select a project first';
  }

  const isCategorySelectDisabled = !selectedProject || categoryTagOptions.length === 0;

  return (
    <div style={{ display: 'flex', gap: '16px', flexWrap: 'wrap' }}>
      {!hideProjectSelect && (
        <Form.Item
          name='project_id'
          label='Project'
          rules={[{ required: true, message: 'Please select a project' }]}
          style={{ flex: 1, minWidth: '200px' }}
        >
          <Select onChange={handleProjectChange}>
            {projects.map(project => (
              <Option key={project.id} value={project.id}>
                {project.name}
              </Option>
            ))}
          </Select>
        </Form.Item>
      )}

      <Form.Item
        name='po_number'
        label='PO Number'
        rules={[{ required: true, message: 'Please enter a PO number' }]}
        style={{ flex: 1, minWidth: '200px' }}
      >
        <Input />
      </Form.Item>

      <Form.Item name='description' label='Description' style={{ flex: 2, minWidth: '300px' }}>
        <Input.TextArea rows={1} placeholder='Enter description' />
      </Form.Item>

      <Form.Item
        name='issue_date'
        label='Issue Date'
        rules={[{ required: true, message: 'Please select an issue date' }]}
        style={{ flex: 1, minWidth: '200px' }}
      >
        <DatePicker style={{ width: '100%' }} />
      </Form.Item>

      <Form.Item
        name='lead_time'
        label='Lead Time (weeks)'
        rules={[{ required: true, message: 'Please enter lead time' }]}
        style={{ flex: 1, minWidth: '150px' }}
      >
        <InputNumber min={1} style={{ width: '100%' }} />
      </Form.Item>

      <Form.Item
        name='amount'
        label='Amount'
        rules={[{ required: true, message: 'Please enter an amount' }]}
        style={{ flex: 1, minWidth: '150px' }}
      >
        <InputNumber
          formatter={value => `$ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
          parser={value => value!.replace(/\$\s?|(,*)/g, '')}
          style={{ width: '100%' }}
        />
      </Form.Item>

      <Form.Item
        name='terms'
        label='Payment Terms'
        rules={[{ required: true, message: 'Please select payment terms' }]}
        initialValue='100% due upon receipt'
        style={{ flex: 1, minWidth: '200px' }}
      >
        <Select>
          <Option value='100% due upon receipt'>100% due upon receipt</Option>
          <Option value='Net 30'>Net 30</Option>
          <Option value='Net 60'>Net 60</Option>
          <Option value='Net 90'>Net 90</Option>
        </Select>
      </Form.Item>

      <Form.Item
        name='category_tag_name'
        label='Expense Category'
        style={{ flex: 1, minWidth: '200px' }}
        rules={[{ required: true, message: 'Please select an expense category' }]}
      >
        <Select
          showSearch
          disabled={isCategorySelectDisabled}
          placeholder={categoryPlaceholder}
          options={categoryTagOptions}
          filterOption={(inputValue, option) =>
            option?.label.toUpperCase().indexOf(inputValue.toUpperCase()) !== -1
          }
        />
      </Form.Item>

      <Form.Item name='tag_names' label='Tags' style={{ flex: 2, minWidth: '200px' }}>
        <Select
          mode='tags'
          style={{ width: '100%' }}
          placeholder='Select or create tags'
          tokenSeparators={[',']}
          options={tagOptions}
          filterOption={(inputValue, option) =>
            option?.label.toUpperCase().indexOf(inputValue.toUpperCase()) !== -1
          }
        />
      </Form.Item>

      {/* Hidden due_date field to store the calculated value */}
      <Form.Item name='due_date' hidden>
        <Input />
      </Form.Item>

      {/* Calculated Due Date Display */}
      <Form.Item label='Calculated Due Date' style={{ flex: 1, minWidth: '200px' }}>
        <div
          style={{
            padding: '0 11px',
            border: '1px solid #d9d9d9',
            borderRadius: '6px',
            backgroundColor: '#fafafa',
            minHeight: '30px',
            display: 'flex',
            alignItems: 'center',
          }}
        >
          {calculatedDueDate ? calculatedDueDate.format('YYYY-MM-DD') : <span></span>}
        </div>
        <span style={{ color: '#999' }}>Fill in issue date, lead time, and terms</span>
      </Form.Item>

      {/* <Form.Item
        name={['project_expense', 'id']}
        label='Link to Project Expense'
        style={{ flex: 1, minWidth: '200px' }}
      >
        <Select allowClear disabled={!selectedProject}>
          {projectExpenses.map(cost => (
            <Option key={cost.id} value={cost.id}>
              {cost.description} - {formatCurrency(cost.amount)}
            </Option>
          ))}
        </Select>
      </Form.Item> */}
    </div>
  );
};

export default PurchaseOrderFormFields;
