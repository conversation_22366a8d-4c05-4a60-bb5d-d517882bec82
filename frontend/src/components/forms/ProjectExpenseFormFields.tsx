import { DatePicker, Form, Input, InputNumber, Select } from 'antd';
import dayjs from 'dayjs';
import React, { useEffect } from 'react';
import { ProjectExpense } from '../../types';

interface ProjectExpenseFormFieldsProps {
  tagOptions: { label: string; value: string }[];
  initialValues?: ProjectExpense | null;
  isEditing?: boolean;
  form?: any;
}

const ProjectExpenseFormFields: React.FC<ProjectExpenseFormFieldsProps> = ({
  tagOptions,
  initialValues,
  isEditing = false,
  form,
}) => {
  // Set initial values when component mounts or initialValues change
  useEffect(() => {
    if (form && initialValues && isEditing) {
      form.setFieldsValue({
        project_id: initialValues.project_id,
        description: initialValues.description,
        amount: initialValues.amount,
        date: initialValues.date ? dayjs(initialValues.date) : null,
        category_tag_name: initialValues.category_tag ? [initialValues.category_tag.name] : [],
        tag_names: initialValues.tags ? initialValues.tags.map(tag => tag.name) : [],
      });
    } else if (!isEditing) {
      // Ensure form is cleared when not editing (new modal)
      form?.resetFields();
    }
  }, [form, initialValues, isEditing]);

  return (
    <>
      <Form.Item
        name='description'
        label='Description'
        rules={[{ required: true, message: 'Please enter a description' }]}
      >
        <Input.TextArea rows={1} placeholder='Enter description' />
      </Form.Item>
      <Form.Item
        name='amount'
        label='Amount'
        rules={[{ required: true, message: 'Please enter an amount' }]}
      >
        <InputNumber
          formatter={value => `$ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
          parser={value => value!.replace(/\$\s?|(,*)/g, '')}
          style={{ width: '100%' }}
        />
      </Form.Item>
      <Form.Item
        name='date'
        label='Date'
        rules={[{ required: true, message: 'Please select a date' }]}
      >
        <DatePicker style={{ width: '100%' }} />
      </Form.Item>
      <Form.Item
        name='category_tag_name'
        label='Expense Category'
        rules={[{ required: true, message: 'Please select an expense category' }]}
      >
        <Select
          mode='tags'
          maxCount={1}
          showSearch
          style={{ width: '100%' }}
          placeholder='Select or create expense category'
          tokenSeparators={[',']}
          options={tagOptions}
          filterOption={(inputValue, option) =>
            option?.label.toUpperCase().indexOf(inputValue.toUpperCase()) !== -1
          }
        />
      </Form.Item>
      <Form.Item name='tag_names' label='Tags' style={{ flex: 2, minWidth: '200px' }}>
        <Select
          mode='tags'
          style={{ width: '100%' }}
          placeholder='Select or create tags'
          tokenSeparators={[',']}
          options={tagOptions}
          filterOption={(inputValue, option) =>
            option?.label.toUpperCase().indexOf(inputValue.toUpperCase()) !== -1
          }
        />
      </Form.Item>
    </>
  );
};

export default ProjectExpenseFormFields;
