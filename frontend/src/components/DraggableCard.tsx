import { DeleteOutlined, EditOutlined, PlusOutlined } from '@ant-design/icons';
import { Card, Col, Dropdown, Modal, Row, Tag, Typography } from 'antd';
import dayjs from 'dayjs';
import React, { useState } from 'react';
import { useDrag } from 'react-dnd';
import { Invoice, Milestone, ProjectExpense, PurchaseOrder } from '../types';
import CreateModal from './CreateModal';
import EditModal from './EditModal';

type Item = Milestone | ProjectExpense | PurchaseOrder | Invoice;
type ItemType = 'milestone' | 'projectExpense' | 'purchaseOrder' | 'invoice';
interface DraggableCardProps {
  item: Item;
  type: ItemType;
  weekDate: string;
  projectId: number;
  onEdit: (item: Milestone | ProjectExpense | PurchaseOrder | Invoice) => void;
  onAddItem: (item: Milestone | ProjectExpense | PurchaseOrder | Invoice) => void;
  onDelete: (item: Item, type: ItemType) => void;
  projectName: string;
  project?: any; // Optional project object for looking up related purchase orders
}

const DraggableCard: React.FC<DraggableCardProps> = ({
  item,
  type,
  weekDate,
  projectId,
  onEdit,
  onAddItem,
  onDelete,
  projectName,
  project,
}) => {
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [createModalType, setCreateModalType] = useState<ItemType>('milestone');
  const [isHovered, setIsHovered] = useState(false);

  const [{ isDragging }, drag] = useDrag(() => ({
    type: type,
    item: { ...item, weekDate, projectId },
    collect: monitor => ({
      isDragging: !!monitor.isDragging(),
    }),
  }));

  const handleEditSave = async (
    updatedItem: Milestone | ProjectExpense | PurchaseOrder | Invoice
  ) => {
    try {
      await onEdit(updatedItem);
      setEditModalVisible(false);
    } catch (error) {
      console.error('Error updating item:', error);
    }
  };

  const handleCreateSave = async (values: Milestone | ProjectExpense | PurchaseOrder | Invoice) => {
    try {
      await onAddItem(values);
      setCreateModalVisible(false);
    } catch (error) {
      console.error('Error creating item:', error);
    }
  };

  const handleDelete = () => {
    const itemTypeName = {
      milestone: 'milestone',
      projectExpense: 'project expense',
      purchaseOrder: 'purchase order',
      invoice: 'invoice',
    }[type];

    Modal.confirm({
      title: `Delete ${itemTypeName}`,
      content: `Are you sure you want to delete this ${itemTypeName}?`,
      okText: 'Yes, Delete',
      okType: 'danger',
      cancelText: 'Cancel',
      onOk: () => {
        onDelete(item, type);
      },
    });
  };

  const menuItems = [
    {
      key: 'edit',
      label: 'Edit',
      icon: <EditOutlined />,
      onClick: () => setEditModalVisible(true),
    },
    {
      key: 'delete',
      label: 'Delete',
      icon: <DeleteOutlined />,
      onClick: handleDelete,
      danger: true,
    },
    {
      key: 'add_milestone',
      label: 'Add Milestone',
      icon: <PlusOutlined />,
      onClick: () => {
        setCreateModalType('milestone');
        setCreateModalVisible(true);
      },
    },
    {
      key: 'add_project_expense',
      label: 'Add Project Expense',
      icon: <PlusOutlined />,
      onClick: () => {
        setCreateModalType('projectExpense');
        setCreateModalVisible(true);
      },
    },
    {
      key: 'add_purchase_order',
      label: 'Add Purchase Order',
      icon: <PlusOutlined />,
      onClick: () => {
        setCreateModalType('purchaseOrder');
        setCreateModalVisible(true);
      },
    },
    {
      key: 'add_invoice',
      label: 'Add Invoice',
      icon: <PlusOutlined />,
      onClick: () => {
        setCreateModalType('invoice');
        setCreateModalVisible(true);
      },
    },
  ];

  // Define colors and styles for different types
  const getTypeStyles = () => {
    switch (type) {
      case 'milestone':
        return {
          borderColor: '#52c41a',
          hoverBgColor: 'rgba(82, 196, 26, 0.1)',
          tagColor: 'success',
          tagText: 'Revenue',
          icon: '💰',
        };
      case 'projectExpense':
        return {
          borderColor: '#1890ff',
          hoverBgColor: 'rgba(24, 144, 255, 0.1)',
          tagColor: 'processing',
          tagText: 'Projected',
          icon: '📝',
        };
      case 'purchaseOrder':
        return {
          borderColor: '#fa8c16',
          hoverBgColor: 'rgba(250, 140, 22, 0.1)',
          tagColor: 'warning',
          tagText: 'Expected',
          icon: '📋',
        };
      case 'invoice':
        return {
          borderColor: '#f5222d',
          hoverBgColor: 'rgba(245, 34, 45, 0.1)',
          tagColor: 'error',
          tagText: 'Debt',
          icon: '🧾',
        };
      default:
        return {
          borderColor: '#d9d9d9',
          hoverBgColor: 'rgba(217, 217, 217, 0.1)',
          tagColor: 'default',
          tagText: 'Unknown',
          icon: '❓',
        };
    }
  };

  const typeStyles = getTypeStyles();
  const borderColor = typeStyles.borderColor;
  const hoverBgColor = typeStyles.hoverBgColor;

  // Helper function to get category name from item
  const getCategoryName = (item: any) => {
    if (item.category_tag && item.category_tag.name) {
      return item.category_tag.name;
    }

    // Special case for invoices: inherit category from related purchase order
    if (type === 'invoice' && item.purchase_order_id && project?.purchase_orders) {
      const relatedPO = project.purchase_orders.find((po: any) => po.id === item.purchase_order_id);
      if (relatedPO?.category_tag?.name) {
        return relatedPO.category_tag.name;
      }
    }

    return null;
  };

  const categoryName = getCategoryName(item);

  return (
    <>
      <Dropdown menu={{ items: menuItems }} trigger={['contextMenu']}>
        <div
          ref={drag}
          style={{
            opacity: isDragging ? 0.5 : 1,
            cursor: 'move',
            transition: 'all 0.2s ease',
            transform: isHovered ? 'scale(1.02)' : 'scale(1)',
            boxShadow: isHovered ? '0 2px 8px rgba(0,0,0,0.15)' : 'none',
            width: '100%',
          }}
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
          onContextMenu={e => e.stopPropagation()} // Stop event propagation here
        >
          <Card
            size='small'
            style={{
              marginBottom: '4px',
              borderLeft: `4px solid ${borderColor}`,
              backgroundColor: isHovered ? hoverBgColor : 'transparent',
              transition: 'all 0.2s ease',
              height: '100%',
            }}
          >
            <Row align='middle' gutter={8}>
              <Col flex='auto'>
                <Row align='middle' gutter={4}>
                  <Col>
                    <span style={{ fontSize: '14px' }}>{typeStyles.icon}</span>
                  </Col>
                  <Col flex='1'>
                    <Typography.Text style={{ fontSize: '12px', lineHeight: '1.2' }}>
                      {item.displayTitle}
                    </Typography.Text>
                  </Col>
                </Row>
                <Row align='middle' gutter={4}>
                  <Col flex='1'>
                    <Typography.Text style={{ fontSize: '12px', lineHeight: '1.2' }}>
                      {item.displayDescription}
                    </Typography.Text>
                  </Col>
                </Row>
              </Col>
            </Row>
            <Row align='middle' gutter={8} justify='space-between'>
              <Col>
                <Typography.Text strong style={{ fontSize: '12px' }}>
                  ${item.amount.toLocaleString()}
                </Typography.Text>
              </Col>
              <Col>
                <Typography.Text type='secondary' style={{ fontSize: '10px' }}>
                  {item.date && dayjs(item.date).format('MM/DD/YYYY')}
                </Typography.Text>
              </Col>
            </Row>
            <Row justify='space-between' style={{ marginTop: 4 }}>
              <Col>
                <div
                  style={{
                    display: 'flex',
                    gap: '4px',
                    flexWrap: 'wrap',
                    justifyContent: 'flex-end',
                  }}
                >
                  <Tag color={typeStyles.tagColor} style={{ fontSize: '10px' }}>
                    {typeStyles.tagText}
                  </Tag>
                  {categoryName && (
                    <Tag color='default' style={{ fontSize: '10px' }}>
                      🏷️ {categoryName}
                    </Tag>
                  )}
                </div>
              </Col>
            </Row>
          </Card>
        </div>
      </Dropdown>
      <EditModal
        visible={editModalVisible}
        onCancel={() => setEditModalVisible(false)}
        onSave={handleEditSave}
        item={item}
        type={type}
      />
      <CreateModal
        visible={createModalVisible}
        onCancel={() => setCreateModalVisible(false)}
        onSave={handleCreateSave}
        projectName={projectName}
        type={createModalType}
        date={weekDate}
        projectId={projectId}
      />
    </>
  );
};

export default React.memo(
  DraggableCard,
  (prevProps: DraggableCardProps, nextProps: DraggableCardProps) => {
    // If the item reference is the same, no re-render needed
    if (prevProps.item === nextProps.item) {
      return true;
    }

    // Check if essential props have changed
    if (
      prevProps.type !== nextProps.type ||
      prevProps.weekDate !== nextProps.weekDate ||
      prevProps.projectId !== nextProps.projectId ||
      prevProps.projectName !== nextProps.projectName ||
      prevProps.project !== nextProps.project
    ) {
      return false;
    }

    const prevItem = prevProps.item as any;
    const nextItem = nextProps.item as any;

    // Check if essential item properties have changed
    if (
      prevItem.id !== nextItem.id ||
      prevItem.description !== nextItem.description ||
      prevItem.displayDescription !== nextItem.displayDescription ||
      prevItem.amount !== nextItem.amount ||
      prevItem.date !== nextItem.date ||
      prevItem.purchase_order_id !== nextItem.purchase_order_id
    ) {
      return false;
    }

    // Check if category_tag has changed (comparing the entire object)
    if (prevItem.category_tag !== nextItem.category_tag) {
      return false;
    }

    return true;
  }
);
