import { CloseOutlined, DeleteOutlined, EditOutlined, SaveOutlined } from '@ant-design/icons';
import {
  Tag as AntdTag,
  Button,
  Card,
  DatePicker,
  Form,
  Input,
  InputNumber,
  message,
  Popconfirm,
  Select,
  Space,
  Spin,
  Table,
  Typography,
} from 'antd';
import dayjs from 'dayjs';
import React, { useEffect, useState } from 'react';
import ClearFiltersButton from '../components/common/clearFiltersButton';
import InvoiceFormFields from '../components/forms/InvoiceFormFields';
import { useOrganization } from '../contexts/OrganizationContext';
import { useTableControls } from '../hooks/useTableControls';
import {
  addInvoice,
  createTag,
  deleteInvoice,
  getInvoices,
  getProjectCategoryTags,
  getPurchaseOrders,
  getTags,
  updateInvoice,
} from '../services/api';
import { Invoice, Project, PurchaseOrder, Tag, UpsertInvoice } from '../types';
import { formatCurrency, parseCurrency } from '../utils/helpers';
import {
  getColumnSearchProps,
  getDateRangeSearchProps,
  getNumericColumnSearchProps,
  getTagColumnSearchProps,
} from '../utils/tableFilters';

const { Title } = Typography;

const Invoices: React.FC = () => {
  const [form] = Form.useForm<UpsertInvoice>();
  const [editForm] = Form.useForm<UpsertInvoice>();
  const [invoices, setInvoices] = useState<Invoice[]>([]);
  const [purchaseOrders, setPurchaseOrders] = useState<PurchaseOrder[]>([]);
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [submitting, setSubmitting] = useState<boolean>(false);
  const [editingKey, setEditingKey] = useState<string | number>('');
  const [selectedProject, setSelectedProject] = useState<number | null>(null);
  const [allTags, setAllTags] = useState<Tag[]>([]);
  const [tagOptions, setTagOptions] = useState<{ label: string; value: string }[]>([]);
  const [categoryTagOptions, setCategoryTagOptions] = useState<{ label: string; value: string }[]>(
    []
  );
  const [editCategoryValue, setEditCategoryValue] = useState<string | undefined>();
  const [editPurchaseOrderValue, setEditPurchaseOrderValue] = useState<number | undefined>();
  const { organization } = useOrganization();

  // Use the custom hook for table controls
  const { filteredInfo, sortedInfo, handleTableChange, clearFiltersAndSorting } = useTableControls({
    initialSort: { columnKey: 'due_date', order: 'descend' },
  });

  useEffect(() => {
    fetchInvoices();
    fetchProjects();
    fetchAllTags();
  }, []);

  useEffect(() => {
    if (selectedProject) {
      fetchProjectCategoryTags(selectedProject);
    }
  }, [selectedProject]);

  // Add CSS for top alignment
  React.useEffect(() => {
    const style = document.createElement('style');
    style.textContent = `
      .invoices-table .ant-table-tbody > tr > td {
        vertical-align: top !important;
        padding-top: 8px !important;
      }
      .invoices-table .ant-form-item {
        margin-bottom: 0 !important;
      }
    `;
    document.head.appendChild(style);
    return () => {
      document.head.removeChild(style);
    };
  }, []);

  const fetchProjectCategoryTags = async (projectId: number) => {
    try {
      const response = await getProjectCategoryTags(projectId);
      const categoryTags = response.data;
      setCategoryTagOptions(
        categoryTags.map(tag => ({
          label: tag.name,
          value: tag.name,
        }))
      );
    } catch (error) {
      console.error('Error fetching project category tags:', error);
      setCategoryTagOptions([]);
    }
  };

  const fetchAllTags = async () => {
    try {
      const response = await getTags();
      const sortedTags = response.data.sort((a: Tag, b: Tag) => a.name.localeCompare(b.name));
      setAllTags(sortedTags);
      setTagOptions(
        sortedTags.map(tag => ({
          label: tag.name,
          value: tag.name,
        }))
      );
    } catch (error: any) {
      console.error('Error fetching tags:', error);
      message.error('Failed to load tags');
    }
  };

  const fetchInvoices = async (): Promise<void> => {
    try {
      setLoading(true);
      const response = await getInvoices();
      const data = response.data;
      const invoicesData: Invoice[] = data.map((invoice: any) => ({
        ...invoice,
        id: invoice.id,
        key: invoice.id,
      }));
      setInvoices(invoicesData);
    } catch (error: any) {
      console.error('Error fetching invoices:', error);
      const errorMessage =
        error.response?.data?.detail?.msg ||
        error.response?.data?.detail ||
        'Failed to load invoices';
      message.error(errorMessage);
      setInvoices([]);
    } finally {
      setLoading(false);
    }
  };

  const fetchProjects = async (): Promise<void> => {
    try {
      const response = await fetch('/api/purchase-order-projects');
      if (!response.ok) throw new Error('Failed to fetch projects');
      const projectsData = await response.json();
      setProjects(projectsData);
    } catch (error) {
      console.error('Error fetching projects:', error);
      message.error('Failed to load projects');
      setProjects([]);
    }
  };

  const fetchPurchaseOrders = async (projectId: number): Promise<void> => {
    try {
      const response = await getPurchaseOrders(projectId);
      const data = response.data;
      if (!Array.isArray(data)) {
        console.error('Invalid purchase orders data structure:', data);
        setPurchaseOrders([]);
        return;
      }

      setPurchaseOrders(data);
    } catch (error: any) {
      console.error('Error fetching purchase orders:', error);
      const errorMessage = error.response?.data?.detail || 'Failed to load purchase orders';
      message.error(errorMessage);
      setPurchaseOrders([]);
    }
  };

  const isEditing = (record: Invoice): boolean => record.id === editingKey;

  const edit = (record: Invoice): void => {
    editForm.setFieldsValue({
      ...record,
      due_date: dayjs(record.due_date),
      tag_names: record.tags ? record.tags.map(tag => tag.name) : [],
      category_tag_name: record.category_tag?.name,
      purchase_order_id: record.purchase_order_id,
    });
    setEditCategoryValue(record.category_tag?.name);
    setEditPurchaseOrderValue(record.purchase_order_id);
    setEditingKey(record.id as string | number);

    // Fetch purchase orders and category tags for the project when editing
    if (record.project_id) {
      fetchPurchaseOrders(record.project_id);
      fetchProjectCategoryTags(record.project_id);
    }
  };

  const cancel = (): void => {
    setEditingKey('');
  };

  const save = async (id: string | number | undefined): Promise<void> => {
    if (!id) {
      message.error('Invalid invoice ID');
      return;
    }
    try {
      const row = await editForm.validateFields();

      // Form-level validation for mutual exclusivity
      if (!row.category_tag_name && !row.purchase_order_id) {
        message.error('Please select either an expense category or a purchase order');
        return;
      }

      if (row.category_tag_name && row.purchase_order_id) {
        message.error('Cannot have both expense category and purchase order. Please choose one.');
        return;
      }

      const tagIds = await processTags(row.tag_names);
      const categoryTagId = await processCategoryTag(row.category_tag_name?.[0] || '');
      const { tags, tag_names, category_tag_name, ...apiValues } = row;

      const updatedInvoice: UpsertInvoice = {
        ...apiValues,
        id: id as number,
        amount: parseCurrency(row.amount),
        tag_ids: tagIds,
        category_tag_id: categoryTagId,
      };

      await updateInvoice(updatedInvoice);
      message.success('Invoice updated successfully');
      setEditingKey('');
      fetchInvoices();
    } catch (error: any) {
      console.error('Error updating invoice:', error);
      if (error.errorFields) {
        message.error('Please check the form fields and try again');
      } else {
        const errorMessage =
          error.response?.data?.detail?.msg ||
          error.response?.data?.detail ||
          'Failed to update invoice';
        message.error(errorMessage);
      }
    }
  };

  const processTags = async (tags: string[] | undefined): Promise<number[]> => {
    if (!organization) {
      message.error('Organization not found');
      return [];
    }
    try {
      if (!tags) {
        return [];
      }
      const tagIds: number[] = [];
      const newTags: Tag[] = [];
      for (const tagName of tags) {
        const existingTag = allTags.find(tag => tag.name === tagName);
        if (existingTag) {
          tagIds.push(existingTag.id);
        } else {
          const trimmedTagName = tagName.trim();
          const response = await createTag({
            id: 0,
            name: trimmedTagName,
            organization_id: organization.id,
          });
          const tagObject = response.data;
          if (tagObject) {
            tagIds.push(tagObject.id);
            newTags.push(tagObject);
          }
        }
      }

      const updatedTags = [...allTags, ...newTags].sort((a, b) => a.name.localeCompare(b.name));
      setAllTags(updatedTags);
      setTagOptions(
        updatedTags.map(tag => ({
          label: tag.name,
          value: tag.name,
        }))
      );

      return tagIds;
    } catch (error) {
      console.error('Error processing tags:', error);
      message.error('Failed to process tags');
      return [];
    }
  };

  const processCategoryTag = async (tagName: string): Promise<number> => {
    if (!organization) {
      message.error('Organization not found');
      return 0;
    }
    const trimmedTagName = tagName?.trim();
    if (!trimmedTagName) {
      return 0;
    }
    try {
      const existingTag = allTags.find(tag => tag.name === trimmedTagName);
      if (existingTag) {
        return existingTag.id;
      } else {
        const response = await createTag({
          id: 0,
          name: trimmedTagName,
          organization_id: organization.id,
        });
        const tagObject = response.data;
        if (tagObject) {
          return tagObject.id;
        }
      }
      return 0;
    } catch (error) {
      console.error('Error processing expense category tag:', error);
      message.error('Failed to process expense category tag');
      return 0;
    }
  };
  const handleProjectChange = (value: number): void => {
    setSelectedProject(value);
    form.setFieldsValue({
      project_id: value,
      tag_names: [],
      category_tag_name: undefined,
    });
    fetchPurchaseOrders(value);
  };

  const handleSubmit = async (values: UpsertInvoice): Promise<void> => {
    try {
      setSubmitting(true);

      // Form-level validation for mutual exclusivity
      if (!values.category_tag_name && !values.purchase_order_id) {
        message.error('Please select either an expense category or a purchase order');
        setSubmitting(false);
        return;
      }

      if (values.category_tag_name && values.purchase_order_id) {
        message.error('Cannot have both expense category and purchase order. Please choose one.');
        setSubmitting(false);
        return;
      }

      const tagIds = await processTags(values.tag_names);
      const categoryTagId = await processCategoryTag(values.category_tag_name?.[0] || '');
      const { tags, tag_names, category_tag_name, ...apiValues } = values;

      const formattedValues: UpsertInvoice = {
        ...apiValues,
        amount: parseCurrency(values.amount),
        tag_ids: tagIds,
        category_tag_id: categoryTagId,
      };

      if (!formattedValues.project_id) {
        message.error('Project is required.');
        setSubmitting(false);
        return;
      }

      await addInvoice(formattedValues);
      message.success('Invoice added successfully');
      form.resetFields();
      fetchInvoices();
    } catch (error: any) {
      console.error('Error adding invoice:', error);
      let errorMessage = 'Failed to add invoice';
      if (error.response?.data?.detail) {
        if (Array.isArray(error.response.data.detail)) {
          errorMessage = error.response.data.detail.map((err: any) => err.msg).join(', ');
        } else if (typeof error.response.data.detail === 'object') {
          errorMessage =
            error.response.data.detail.msg || JSON.stringify(error.response.data.detail);
        } else {
          errorMessage = error.response.data.detail;
        }
      }
      message.error(errorMessage);
    } finally {
      setSubmitting(false);
    }
  };

  const handleDelete = async (id: string | number | undefined): Promise<void> => {
    if (!id) {
      message.error('Invalid invoice ID');
      return;
    }

    try {
      await deleteInvoice(id);
      message.success('Invoice deleted successfully');
      fetchInvoices();
    } catch (error: any) {
      console.error('Error deleting invoice:', error);
      const errorMessage = error.response?.data?.detail || 'Failed to delete invoice';
      message.error(errorMessage);
    }
  };

  const columns = [
    {
      title: 'Project Name',
      dataIndex: 'project_name',
      key: 'project_name',
      width: '18%',
      align: 'left' as const,
      sorter: (a: Invoice, b: Invoice) => {
        const aName = a.project_name || '';
        const bName = b.project_name || '';
        return aName.localeCompare(bName);
      },
      filteredValue: filteredInfo.project_name || null,
      sortOrder: sortedInfo.columnKey === 'project_name' ? sortedInfo.order : undefined,
      ...getColumnSearchProps('project_name', 'Project Name'),
    },
    {
      title: 'Invoice Number',
      dataIndex: 'invoice_number',
      key: 'invoice_number',
      width: '12%',
      align: 'left' as const,
      sorter: (a: Invoice, b: Invoice) => {
        const aInvoiceNumber = a.invoice_number || '';
        const bInvoiceNumber = b.invoice_number || '';
        return aInvoiceNumber.localeCompare(bInvoiceNumber);
      },
      filteredValue: filteredInfo.invoice_number || null,
      sortOrder: sortedInfo.columnKey === 'invoice_number' ? sortedInfo.order : undefined,
      ...getColumnSearchProps('invoice_number', 'Invoice Number'),
      render: (invoice_number: string, record: Invoice) => {
        const editable = isEditing(record);
        return editable ? (
          <Form.Item name='invoice_number' style={{ marginBottom: 0 }}>
            <Input placeholder='Enter invoice number' />
          </Form.Item>
        ) : (
          invoice_number || '-'
        );
      },
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
      width: '18%',
      align: 'left' as const,
      sorter: (a: Invoice, b: Invoice) => {
        const aDescription = a.description || '';
        const bDescription = b.description || '';
        return aDescription.localeCompare(bDescription);
      },
      filteredValue: filteredInfo.description || null,
      sortOrder: sortedInfo.columnKey === 'description' ? sortedInfo.order : undefined,
      ...getColumnSearchProps('description', 'Description'),
      render: (description: string, record: Invoice) => {
        const editable = isEditing(record);
        return editable ? (
          <Form.Item name='description' style={{ marginBottom: 0 }}>
            <Input.TextArea rows={1} placeholder='Enter description' />
          </Form.Item>
        ) : (
          description || '-'
        );
      },
    },
    {
      title: 'Amount',
      dataIndex: 'amount',
      key: 'amount',
      width: '10%',
      align: 'right' as const,
      sorter: (a: Invoice, b: Invoice) => a.amount - b.amount,
      filteredValue: filteredInfo.amount || null,
      sortOrder: sortedInfo.columnKey === 'amount' ? sortedInfo.order : undefined,
      ...getNumericColumnSearchProps('amount', true),
      render: (amount: number, record: Invoice) => {
        const editable = isEditing(record);
        return editable ? (
          <Form.Item
            name='amount'
            rules={[{ required: true, message: 'Please enter an amount' }]}
            style={{ marginBottom: 0 }}
          >
            <InputNumber
              formatter={value => `$ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
              parser={value => value!.replace(/\$\s?|(,*)/g, '')}
              style={{ width: '100%' }}
            />
          </Form.Item>
        ) : (
          formatCurrency(amount)
        );
      },
    },
    {
      title: 'Due Date',
      dataIndex: 'due_date',
      key: 'due_date',
      width: '10%',
      align: 'center' as const,
      sorter: (a: Invoice, b: Invoice) => dayjs(a.due_date).unix() - dayjs(b.due_date).unix(),
      filteredValue: filteredInfo.due_date || null,
      sortOrder: sortedInfo.columnKey === 'due_date' ? sortedInfo.order : undefined,
      ...getDateRangeSearchProps('due_date'),
      defaultSortOrder: 'descend' as const,
      render: (date: dayjs.Dayjs, record: Invoice) => {
        const editable = isEditing(record);
        return editable ? (
          <Form.Item
            name='due_date'
            rules={[{ required: true, message: 'Please select a due date' }]}
            style={{ marginBottom: 0 }}
          >
            <DatePicker style={{ width: '100%' }} />
          </Form.Item>
        ) : (
          date.format('MM/DD/YYYY')
        );
      },
    },
    {
      title: 'Purchase Order',
      dataIndex: 'purchase_order_id',
      key: 'purchase_order_id',
      width: '12%',
      align: 'left' as const,
      render: (purchase_order_id: number | undefined, record: Invoice) => {
        const editable = isEditing(record);
        const hasBothFields = editCategoryValue && editPurchaseOrderValue;

        return editable ? (
          <Form.Item
            name='purchase_order_id'
            extra={
              hasBothFields ? (
                <span style={{ color: '#ff4d4f' }}>
                  ⚠️ Cannot have both expense category and purchase order. Please choose only one.
                </span>
              ) : null
            }
            validateStatus={hasBothFields ? 'error' : undefined}
            style={{ marginBottom: 0 }}
          >
            <Select
              showSearch
              allowClear
              placeholder={
                purchaseOrders.length === 0
                  ? 'No purchase orders available'
                  : 'Select purchase order'
              }
              options={purchaseOrders.map(po => ({
                label: `${po.po_number} - ${po.description || 'No description'}`,
                value: po.id,
              }))}
              filterOption={(inputValue, option) =>
                option?.label.toUpperCase().indexOf(inputValue.toUpperCase()) !== -1
              }
              onChange={value => {
                setEditPurchaseOrderValue(value);
              }}
            />
          </Form.Item>
        ) : (
          record.po_number || '-'
        );
      },
    },
    {
      title: 'Expense Category',
      dataIndex: 'category_tag',
      key: 'category_tag',
      width: '12%',
      align: 'left' as const,
      render: (category_tag: Tag | undefined, record: Invoice) => {
        const editable = isEditing(record);
        const hasBothFields = editCategoryValue && editPurchaseOrderValue;

        return editable ? (
          <Form.Item
            name='category_tag_name'
            extra={
              hasBothFields ? (
                <span style={{ color: '#ff4d4f' }}>
                  ⚠️ Cannot have both expense category and purchase order. Please choose only one.
                </span>
              ) : null
            }
            validateStatus={hasBothFields ? 'error' : undefined}
            style={{ marginBottom: 0 }}
          >
            <Select
              showSearch
              allowClear
              placeholder={
                categoryTagOptions.length === 0
                  ? 'No expense categories available'
                  : 'Select category'
              }
              options={categoryTagOptions}
              filterOption={(inputValue, option) =>
                option?.label.toUpperCase().indexOf(inputValue.toUpperCase()) !== -1
              }
              onChange={value => {
                setEditCategoryValue(value);
              }}
            />
          </Form.Item>
        ) : category_tag ? (
          <AntdTag color='blue'>{category_tag.name}</AntdTag>
        ) : (
          <span style={{ color: '#999' }}>No category</span>
        );
      },
    },
    {
      title: 'Tags',
      dataIndex: 'tags',
      key: 'tags',
      width: '12%',
      align: 'left' as const,
      ...getTagColumnSearchProps(allTags),
      filteredValue: filteredInfo.tags || null,
      render: (tags: Tag[] | undefined, record: Invoice) => {
        const editable = isEditing(record);
        return editable ? (
          <Form.Item name='tag_names' style={{ marginBottom: 0 }}>
            <Select
              mode='tags'
              style={{ width: '100%' }}
              placeholder='Select or create tags'
              tokenSeparators={[',']}
              options={tagOptions}
            />
          </Form.Item>
        ) : (
          <>
            {(tags || []).map(tag => (
              <AntdTag key={tag.id}>{tag.name}</AntdTag>
            ))}
          </>
        );
      },
    },
    {
      title: 'Actions',
      key: 'actions',
      width: '8%',
      align: 'center' as const,
      render: (_: any, record: Invoice) => {
        const editable = isEditing(record);
        return editable ? (
          <Space>
            <Button
              icon={<SaveOutlined />}
              type='primary'
              onClick={() => save(record.id)}
              size='small'
            >
              Save
            </Button>
            <Button icon={<CloseOutlined />} onClick={cancel} size='small'>
              Cancel
            </Button>
          </Space>
        ) : (
          <Space>
            <Button
              icon={<EditOutlined />}
              type='primary'
              disabled={editingKey !== ''}
              onClick={() => edit(record)}
              size='small'
            >
              Edit
            </Button>
            <Popconfirm
              title='Are you sure you want to delete this invoice?'
              onConfirm={() => handleDelete(record.id)}
              okText='Yes'
              cancelText='No'
            >
              <Button danger icon={<DeleteOutlined />} size='small' disabled={editingKey !== ''}>
                Delete
              </Button>
            </Popconfirm>
          </Space>
        );
      },
    },
  ];

  return (
    <div>
      <Title level={2}>Invoices</Title>

      <Card title='Add Invoice' style={{ marginBottom: 16 }}>
        <Form form={form} layout='vertical' onFinish={handleSubmit}>
          <InvoiceFormFields
            projects={projects}
            purchaseOrders={purchaseOrders}
            tagOptions={tagOptions}
            categoryTagOptions={categoryTagOptions}
            handleProjectChange={handleProjectChange}
            isEditing={false}
            selectedProject={selectedProject}
          />
          <Form.Item
            style={{
              alignSelf: 'flex-end',
              marginBottom: '0',
              flex: 1,
              minWidth: '150px',
            }}
          >
            <Button
              type='primary'
              htmlType='submit'
              loading={submitting}
              style={{ width: '100%', marginTop: '24px' }}
            >
              Add Invoice
            </Button>
          </Form.Item>
        </Form>
      </Card>

      <Card title='Invoices'>
        <ClearFiltersButton onClear={clearFiltersAndSorting} />
        <Form form={editForm} component={false}>
          {loading ? (
            <div style={{ textAlign: 'center', padding: '20px' }}>
              <Spin size='large' />
            </div>
          ) : (
            <Table
              columns={columns}
              dataSource={invoices}
              rowKey='id'
              pagination={{ defaultPageSize: 10, showSizeChanger: true }}
              scroll={{ x: 'max-content' }}
              onChange={handleTableChange}
              className='invoices-table'
            />
          )}
        </Form>
      </Card>
    </div>
  );
};

export default Invoices;
