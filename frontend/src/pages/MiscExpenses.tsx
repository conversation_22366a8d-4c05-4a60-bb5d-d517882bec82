import { CloseOutlined, DeleteOutlined, EditOutlined, SaveOutlined } from '@ant-design/icons';
import {
  Tag as AntdTag,
  Button,
  Card,
  DatePicker,
  Form,
  Input,
  message,
  Popconfirm,
  Select,
  Space,
  Table,
  Typography,
} from 'antd';
import dayjs from 'dayjs';
import React, { useEffect, useState } from 'react';
import ClearFiltersButton from '../components/common/clearFiltersButton';
import { CurrencyInput } from '../components/common/CurrencyInput';
import { LoadingTable } from '../components/common/LoadingTable';
import { useOrganization } from '../contexts/OrganizationContext';
import { useTableControls } from '../hooks/useTableControls';
import {
  addMiscExpense,
  createTag,
  deleteMiscExpense,
  getMiscExpenses,
  getTags,
  updateMiscExpense,
} from '../services/api';
import { MiscExpense, Tag, UpsertMiscExpense } from '../types';
import { TableColumnType } from '../types/table';
import { formatCurrency, parseCurrency } from '../utils/helpers';
import {
  getColumnSearchProps,
  getDateRangeSearchProps,
  getNumericColumnSearchProps,
  getTagColumnSearchProps,
} from '../utils/tableFilters';

const { Title } = Typography;

const MiscExpenses: React.FC = () => {
  const [form] = Form.useForm<UpsertMiscExpense>();
  const [expenses, setExpenses] = useState<MiscExpense[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [submitting, setSubmitting] = useState<boolean>(false);
  const [editingKey, setEditingKey] = useState<string | number>('');
  const [editForm] = Form.useForm();
  const [total, setTotal] = useState<number>(0);
  const [allTags, setAllTags] = useState<Tag[]>([]);
  const [tagOptions, setTagOptions] = useState<{ label: string; value: string }[]>([]);
  const { filteredInfo, sortedInfo, handleTableChange, clearFiltersAndSorting } = useTableControls({
    initialSort: { columnKey: 'date', order: 'descend' },
  });
  const { organization } = useOrganization();

  useEffect(() => {
    fetchExpenses();
    fetchAllTags();
  }, []);

  useEffect(() => {
    const sum = expenses.reduce((acc, expense) => acc + expense.amount, 0);
    setTotal(sum);
  }, [expenses]);

  // Add CSS for table cell alignment
  useEffect(() => {
    const style = document.createElement('style');
    style.textContent = `
      .misc-expenses-table .ant-table-tbody > tr > td {
        vertical-align: top !important;
        padding-top: 8px !important;
      }
      .misc-expenses-table .ant-form-item {
        margin-bottom: 0 !important;
      }
    `;
    document.head.appendChild(style);
    return () => {
      document.head.removeChild(style);
    };
  }, []);

  const fetchAllTags = async () => {
    try {
      const response = await getTags();
      const sortedTags = response.data.sort((a: Tag, b: Tag) => a.name.localeCompare(b.name));
      setAllTags(sortedTags);
      setTagOptions(
        sortedTags.map(tag => ({
          label: tag.name,
          value: tag.name,
        }))
      );
    } catch (error: any) {
      console.error('Error fetching tags:', error);
      message.error('Failed to load tags');
    }
  };

  const fetchExpenses = async (): Promise<void> => {
    try {
      setLoading(true);
      const response = await getMiscExpenses();
      const expensesData = Array.isArray(response.data) ? response.data : [];
      setExpenses(expensesData);
    } catch (error: any) {
      console.error('Error fetching expenses:', error);
      message.error('Failed to load expenses');
      setExpenses([]);
    } finally {
      setLoading(false);
    }
  };

  const processTags = async (tags: string[] | undefined): Promise<number[]> => {
    if (!organization) {
      message.error('Organization not found');
      return [];
    }
    try {
      if (!tags) {
        return [];
      }
      const tagIds: number[] = [];
      const newTags: Tag[] = [];
      for (const tagName of tags) {
        const existingTag = allTags.find(tag => tag.name === tagName);
        if (existingTag) {
          tagIds.push(existingTag.id);
        } else {
          const trimmedTagName = tagName.trim();
          const response = await createTag({
            id: 0,
            name: trimmedTagName,
            organization_id: organization.id,
          });
          const tagObject = response.data;
          if (tagObject) {
            tagIds.push(tagObject.id);
            newTags.push(tagObject);
          }
        }
      }

      const updatedTags = [...allTags, ...newTags].sort((a, b) => a.name.localeCompare(b.name));
      setAllTags(updatedTags);
      setTagOptions(
        updatedTags.map(tag => ({
          label: tag.name,
          value: tag.name,
        }))
      );

      return tagIds;
    } catch (error) {
      console.error('Error processing tags:', error);
      message.error('Failed to process tags');
      return [];
    }
  };

  const handleSubmit = async (values: UpsertMiscExpense): Promise<void> => {
    try {
      setSubmitting(true);
      const tagIds = await processTags(values.tag_names);
      const { tags, tag_names, ...rest } = values;
      const formattedValues = {
        ...rest,
        amount: parseCurrency(values.amount),
        tag_ids: tagIds,
      };
      await addMiscExpense(formattedValues as UpsertMiscExpense);
      message.success('Miscellaneous expense added successfully');
      form.resetFields();
      await fetchExpenses();
    } catch (error) {
      console.error('Error adding expense:', error);
      message.error('Failed to add expense');
    } finally {
      setSubmitting(false);
    }
  };

  const handleDelete = async (id: string | number): Promise<void> => {
    try {
      await deleteMiscExpense(id);
      message.success('Miscellaneous expense deleted successfully');
      await fetchExpenses();
    } catch (error) {
      console.error('Error deleting expense:', error);
      message.error('Failed to delete expense');
    }
  };

  const isEditing = (record: MiscExpense): boolean => record.id === editingKey;

  const edit = (record: MiscExpense): void => {
    editForm.setFieldsValue({
      ...record,
      tag_names: record.tags ? record.tags.map(tag => tag.name) : [],
    });
    setEditingKey(record.id);
  };

  const cancel = (): void => {
    setEditingKey('');
  };

  const save = async (id: string | number): Promise<void> => {
    try {
      const row = (await editForm.validateFields()) as UpsertMiscExpense;
      const tagIds = await processTags(row.tag_names);
      const { tags, tag_names, id: rowId, ...rest } = row;
      const updatedExpense = {
        id: id as number,
        ...rest,
        tag_ids: tagIds,
      };

      await updateMiscExpense(updatedExpense as UpsertMiscExpense);
      message.success('Miscellaneous expense updated successfully');
      setEditingKey('');
      await fetchExpenses();
    } catch (error) {
      console.error('Error updating expense:', error);
      message.error('Failed to update expense');
    }
  };

  const columns: TableColumnType<MiscExpense>[] = [
    {
      title: 'Date',
      dataIndex: 'date',
      key: 'date',
      width: '15%',
      sorter: (a, b) => a.date.unix() - b.date.unix(),
      sortOrder: sortedInfo.columnKey === 'date' && sortedInfo.order,
      ...getDateRangeSearchProps('date'),
      defaultSortOrder: 'descend' as const,
      render: (date: dayjs.Dayjs, record: MiscExpense) => {
        const editable = isEditing(record);
        return editable ? (
          <Form.Item name='date' rules={[{ required: true }]}>
            <DatePicker style={{ width: '100%' }} />
          </Form.Item>
        ) : (
          date.format('YYYY-MM-DD')
        );
      },
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
      width: '35%',
      sorter: (a, b) => a.description.localeCompare(b.description),
      sortOrder: sortedInfo.columnKey === 'description' && sortedInfo.order,
      ...getColumnSearchProps('description'),
      render: (description: string, record: MiscExpense) => {
        const editable = isEditing(record);
        return editable ? (
          <Form.Item name='description' rules={[{ required: true }]}>
            <Input.TextArea rows={1} placeholder='Enter description' />
          </Form.Item>
        ) : (
          description
        );
      },
    },
    {
      title: 'Amount',
      dataIndex: 'amount',
      key: 'amount',
      width: '15%',
      sorter: (a, b) => a.amount - b.amount,
      sortOrder: sortedInfo.columnKey === 'amount' && sortedInfo.order,
      ...getNumericColumnSearchProps('amount'),
      render: (amount: number, record: MiscExpense) => {
        const editable = isEditing(record);
        return editable ? (
          <Form.Item name='amount' rules={[{ required: true }]}>
            <CurrencyInput />
          </Form.Item>
        ) : (
          formatCurrency(amount)
        );
      },
    },
    {
      title: 'Tags',
      dataIndex: 'tags',
      key: 'tags',
      width: '20%',
      ...getTagColumnSearchProps(allTags),
      render: (tags: Tag[], record: MiscExpense) => {
        const editable = isEditing(record);
        return editable ? (
          <Form.Item name='tag_names'>
            <Select
              mode='tags'
              style={{ width: '100%' }}
              placeholder='Select or create tags'
              tokenSeparators={[',']}
              options={tagOptions}
            />
          </Form.Item>
        ) : (
          <>
            {(tags || []).map(tag => (
              <AntdTag color='blue' key={tag.id}>
                {tag.name}
              </AntdTag>
            ))}
          </>
        );
      },
    },
    {
      title: 'Actions',
      key: 'actions',
      width: '15%',
      render: (_: any, record: MiscExpense) => {
        const editable = isEditing(record);
        return editable ? (
          <Space>
            <Button
              icon={<SaveOutlined />}
              type='primary'
              onClick={() => save(record.id)}
              size='small'
            >
              Save
            </Button>
            <Button icon={<CloseOutlined />} onClick={cancel} size='small'>
              Cancel
            </Button>
          </Space>
        ) : (
          <Space>
            <Button
              icon={<EditOutlined />}
              type='primary'
              disabled={editingKey !== ''}
              onClick={() => edit(record)}
              size='small'
            >
              Edit
            </Button>
            <Popconfirm title='Sure to delete?' onConfirm={() => handleDelete(record.id)}>
              <Button danger icon={<DeleteOutlined />} size='small' disabled={editingKey !== ''}>
                Delete
              </Button>
            </Popconfirm>
          </Space>
        );
      },
    },
  ];

  return (
    <div>
      <Title level={2}>Miscellaneous Expenses</Title>
      <Card>
        <Title level={4}>Add New Expense</Title>
        <Form form={form} onFinish={handleSubmit} layout='vertical'>
          <Form.Item name='date' label='Date' rules={[{ required: true }]}>
            <DatePicker style={{ width: '100%' }} />
          </Form.Item>
          <Form.Item name='description' label='Description' rules={[{ required: true }]}>
            <Input.TextArea rows={1} placeholder='Enter description' />
          </Form.Item>
          <Form.Item name='amount' label='Amount' rules={[{ required: true }]}>
            <CurrencyInput />
          </Form.Item>
          <Form.Item name='tag_names' label='Tags'>
            <Select
              mode='tags'
              style={{ width: '100%' }}
              placeholder='Select or create tags'
              tokenSeparators={[',']}
              options={tagOptions}
            />
          </Form.Item>
          <Form.Item>
            <Button type='primary' htmlType='submit' loading={submitting}>
              Add Expense
            </Button>
          </Form.Item>
        </Form>
      </Card>

      <Card style={{ marginTop: '20px' }}>
        <Title level={4}>Existing Expenses</Title>
        <Form form={editForm} component={false}>
          <ClearFiltersButton onClear={clearFiltersAndSorting} />
          <LoadingTable<MiscExpense>
            loading={loading}
            columns={columns}
            dataSource={expenses}
            rowKey='key'
            pagination={{ defaultPageSize: 10, showSizeChanger: true }}
            onChange={handleTableChange}
            className='misc-expenses-table'
            summary={() => (
              <Table.Summary fixed>
                <Table.Summary.Row>
                  <Table.Summary.Cell index={0} colSpan={2} align='right'>
                    <strong>Total:</strong>
                  </Table.Summary.Cell>
                  <Table.Summary.Cell index={2}>
                    <strong>{formatCurrency(total)}</strong>
                  </Table.Summary.Cell>
                  <Table.Summary.Cell index={3} />
                </Table.Summary.Row>
              </Table.Summary>
            )}
          />
        </Form>
      </Card>
    </div>
  );
};

export default MiscExpenses;
