import dayjs from 'dayjs';

/**
 * Format a number as currency
 * @param {number|string} value - The value to format
 * @param {string} [currency='USD'] - The currency code
 * @param {string} [locale='en-US'] - The locale
 * @returns {string} Formatted currency string
 */
export const formatCurrency = (
  value: number | string | null | undefined,
  currency: string = 'USD',
  locale: string = 'en-US'
): string => {
  if (value === null || value === undefined) return '$0.00';

  // If value is a string with a dollar sign, remove it
  const numericValue = parseCurrency(value);

  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(numericValue);
};

/**
 * Parse a currency string to a number with two decimal places
 * @param {string|number} value - The currency string to parse
 * @returns {number} The parsed number
 */
export const parseCurrency = (value: string | number | null | undefined): number => {
  if (value === null || value === undefined) return 0;

  // If value is already a number, return it
  if (typeof value === 'number') return value;

  // Remove currency symbols, commas, and other non-numeric characters except decimal point
  const numericString = value.toString().replace(/[^0-9.-]/g, '');

  // Parse the string to a float
  const parsedValue = parseFloat(numericString) || 0;
  const twoDecimalPlaces = Math.round(parsedValue * 100) / 100;
  return twoDecimalPlaces;
};

/**
 * Format a date string
 * @param {string|Date} date - The date to format
 * @param {string} [format='YYYY-MM-DD'] - The format to use
 * @returns {string} Formatted date string
 */
export const formatDate = (
  date: string | Date | null | undefined,
  format: string = 'YYYY-MM-DD'
): string => {
  if (!date) return '';

  const d = dayjs(date);
  if (!d.isValid()) return '';

  return d.format(format);
};

/**
 * Format recurrence text
 * @param {string} recurrence - The recurrence type
 * @param {number} [interval] - The interval for custom recurrence
 * @returns {string} Formatted recurrence text
 */
export const formatRecurrence = (
  recurrence: string | null | undefined,
  interval?: number
): string => {
  if (!recurrence || recurrence === 'None') return 'One-time';

  if (recurrence === 'Custom' && interval) {
    return `Every ${interval} days`;
  }

  return recurrence;
};

/**
 * Generate an array of colors for charts
 * @param {number} count - The number of colors needed
 * @returns {string[]} Array of color hex codes
 */
export const generateChartColors = (count: number): string[] => {
  const baseColors: string[] = [
    '#1890ff', // Blue
    '#52c41a', // Green
    '#faad14', // Yellow
    '#f5222d', // Red
    '#722ed1', // Purple
    '#13c2c2', // Cyan
    '#fa8c16', // Orange
    '#eb2f96', // Pink
    '#a0d911', // Lime
    '#fadb14', // Gold
  ];

  // If we need more colors than in our base set, generate them
  if (count <= baseColors.length) {
    return baseColors.slice(0, count);
  }

  // Generate additional colors by rotating hue
  const result = [...baseColors];
  const neededExtra = count - baseColors.length;

  for (let i = 0; i < neededExtra; i++) {
    // Simple algorithm to generate visually distinct colors
    const hue = (i * 137) % 360; // Golden angle approximation
    result.push(`hsl(${hue}, 70%, 50%)`);
  }

  return result;
};

/**
 * Debounce function to limit how often a function can be called
 * @param {Function} func - The function to debounce
 * @param {number} wait - The debounce wait time in milliseconds
 * @returns {Function} Debounced function
 */
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: ReturnType<typeof setTimeout> | null = null;

  return function executedFunction(...args: Parameters<T>): void {
    const later = () => {
      if (timeout) clearTimeout(timeout);
      func(...args);
    };

    if (timeout) clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

/**
 * Calculate the sum of an array of numbers
 * @param {Array<number|string>} values - Array of values to sum
 * @returns {number} The sum
 */
export const calculateSum = (values: Array<number | string>): number => {
  if (!Array.isArray(values)) return 0;

  return values.reduce((sum: number, value: number | string) => {
    // Convert string values to numbers using parseCurrency
    const numValue: number = typeof value === 'string' ? parseCurrency(value) : Number(value);
    return sum + (numValue || 0);
  }, 0);
};

/**
 * Format a number with commas for thousands
 * @param {number|string} value - The value to format
 * @returns {string} Formatted number string
 */
export const formatNumber = (value: number | string | null | undefined): string => {
  if (value === null || value === undefined) return '0';

  const numValue = typeof value === 'string' ? parseFloat(value.replace(/[^0-9.-]/g, '')) : value;

  return new Intl.NumberFormat().format(numValue || 0);
};

// Calculate sum of array values
export const sumArray = <T>(arr: T[] | null | undefined, key: keyof T | null = null): number => {
  if (!arr || !arr.length) return 0;
  if (key) {
    return arr.reduce((sum, item) => sum + (parseCurrency(item[key] as any) || 0), 0);
  }
  return arr.reduce((sum, item) => sum + (parseCurrency(item as any) || 0), 0);
};

// Deep clone an object
export const deepClone = <T>(obj: T): T => {
  return JSON.parse(JSON.stringify(obj));
};

// Get error message from API error
export interface ApiError extends Error {
  response?: {
    data?: {
      detail?: string;
    };
  };
}

export const getErrorMessage = (error: ApiError): string => {
  if (error.response && error.response.data && error.response.data.detail) {
    return error.response.data.detail;
  }
  return error.message || 'An error occurred';
};

// Validate form fields
export const validateFields = (
  values: Record<string, any>,
  requiredFields: string[]
): Record<string, string> => {
  const errors: Record<string, string> = {};
  requiredFields.forEach(field => {
    if (!values[field]) {
      errors[field] = `${
        field.charAt(0).toUpperCase() + field.slice(1).replace('_', ' ')
      } is required`;
    }
  });
  return errors;
};

/**
 * Check if a response is HTML instead of JSON
 * @param {any} data - The response data to check
 * @returns {boolean} True if the response is HTML
 */
export const isHtmlResponse = (data: any): boolean => {
  return (
    typeof data === 'string' &&
    (data.includes('<!DOCTYPE html>') || data.includes('<html') || data.includes('<body'))
  );
};

/**
 * Calculate due date based on issue date, lead time, and payment terms
 * @param {dayjs.Dayjs} issueDate - The issue date
 * @param {number} leadTime - Lead time in weeks
 * @param {string} terms - Payment terms
 * @returns {dayjs.Dayjs} The calculated due date
 */
export const calculateDueDate = (
  issueDate: dayjs.Dayjs,
  leadTime: number,
  terms: string
): dayjs.Dayjs => {
  // Start with issue date + lead time weeks
  let dueDate = issueDate.add(leadTime, 'week');

  // Add additional days based on payment terms
  if (terms === '100% due upon receipt') {
    // No additional days
  } else if (terms === 'Net 30') {
    dueDate = dueDate.add(30, 'day');
  } else if (terms === 'Net 60') {
    dueDate = dueDate.add(60, 'day');
  } else if (terms === 'Net 90') {
    dueDate = dueDate.add(90, 'day');
  } else {
    // Try to extract number from terms like "Net 45" or "45 days"
    const numbers = terms.match(/\d+/);
    if (numbers) {
      dueDate = dueDate.add(parseInt(numbers[0]), 'day');
    }
  }

  return dueDate;
};

/**
 * Calculate issue date based on due date, lead time, and payment terms
 * @param {dayjs.Dayjs} dueDate - The due date
 * @param {number} leadTime - Lead time in weeks
 * @param {string} terms - Payment terms
 * @returns {dayjs.Dayjs} The calculated issue date
 */
export const calculateIssueDate = (
  dueDate: dayjs.Dayjs,
  leadTime: number,
  terms: string
): dayjs.Dayjs => {
  // Start with due date
  let issueDate = dueDate;

  // Subtract additional days based on payment terms
  if (terms === '100% due upon receipt') {
    // No additional days to subtract
  } else if (terms === 'Net 30') {
    issueDate = issueDate.subtract(30, 'day');
  } else if (terms === 'Net 60') {
    issueDate = issueDate.subtract(60, 'day');
  } else if (terms === 'Net 90') {
    issueDate = issueDate.subtract(90, 'day');
  } else {
    // Try to extract number from terms like "Net 45" or "45 days"
    const numbers = terms.match(/\d+/);
    if (numbers) {
      issueDate = issueDate.subtract(parseInt(numbers[0]), 'day');
    }
  }

  // Subtract lead time weeks
  issueDate = issueDate.subtract(leadTime, 'week');

  return issueDate;
};
